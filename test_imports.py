#!/usr/bin/env python3
"""
测试导入脚本 - 验证所有必要的包都能正确导入
"""

try:
    import pandas as pd
    print("✅ pandas 导入成功")
except ImportError as e:
    print(f"❌ pandas 导入失败: {e}")

try:
    import numpy as np
    print("✅ numpy 导入成功")
except ImportError as e:
    print(f"❌ numpy 导入失败: {e}")

try:
    import matplotlib.pyplot as plt
    print("✅ matplotlib.pyplot 导入成功")
except ImportError as e:
    print(f"❌ matplotlib.pyplot 导入失败: {e}")

try:
    import seaborn as sns
    print("✅ seaborn 导入成功")
except ImportError as e:
    print(f"❌ seaborn 导入失败: {e}")

print("\n所有导入测试完成！")
