<?xml version="1.0" encoding="utf-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
  "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Created with matplotlib (http://matplotlib.org/) -->
<svg height="72pt" version="1.1" viewBox="0 0 72 72" width="72pt" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
 <defs>
  <style type="text/css">
*{stroke-linecap:butt;stroke-linejoin:round;}
  </style>
 </defs>
 <g id="figure_1">
  <g id="patch_1">
   <path d="M 0 72 
L 72 72 
L 72 0 
L 0 0 
z
" style="fill:none;opacity:0;"/>
  </g>
  <g id="text_1">
   <path d="M 21.4225 61.72875 
L 21.4225 47.161875 
L 50.566875 47.161875 
L 50.566875 61.72875 
z
M 55.4225 61.72875 
L 55.4225 45.94 
C 55.4225 43.931875 53.796875 42.295625 51.78875 42.295625 
L 20.21125 42.295625 
C 18.203125 42.295625 16.566875 43.931875 16.566875 45.94 
L 16.566875 61.72875 
L 11.71125 61.72875 
L 11.71125 13.161875 
L 16.566875 13.161875 
L 16.566875 28.94 
C 16.566875 30.95875 18.203125 32.584375 20.21125 32.584375 
L 42.066875 32.584375 
C 44.085625 32.584375 45.71125 30.95875 45.71125 28.94 
L 45.71125 13.161875 
C 46.47625 13.161875 47.953125 13.7675 48.484375 14.29875 
L 59.14125 24.955625 
C 59.640625 25.455 60.28875 27.00625 60.28875 27.72875 
L 60.28875 61.72875 
z
M 40.855625 26.5175 
C 40.855625 27.155 40.281875 27.72875 39.644375 27.72875 
L 32.355625 27.72875 
C 31.7075 27.72875 31.144375 27.155 31.144375 26.5175 
L 31.144375 14.373125 
C 31.144375 13.725 31.7075 13.161875 32.355625 13.161875 
L 39.644375 13.161875 
C 40.281875 13.161875 40.855625 13.725 40.855625 14.373125 
z
M 65.144375 27.72875 
C 65.144375 25.720625 64.0075 22.9475 62.5625 21.5025 
L 51.9375 10.8775 
C 50.4925 9.443125 47.719375 8.295625 45.71125 8.295625 
L 10.5 8.295625 
C 8.491875 8.295625 6.855625 9.931875 6.855625 11.94 
L 6.855625 62.94 
C 6.855625 64.95875 8.491875 66.584375 10.5 66.584375 
L 61.5 66.584375 
C 63.508125 66.584375 65.144375 64.95875 65.144375 62.94 
z
" style="fill:black;"/>
  </g>
 </g>
</svg>
